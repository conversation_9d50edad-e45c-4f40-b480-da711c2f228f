# -*- coding: utf-8 -*-
from flask import Blueprint, request, jsonify, send_file, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_, and_
from app import db
from app.models import User, Company, Bid, TenderDocument, BidNode, BidIntermediate, FileStorage, BidReference
from app.file_service import file_storage_service
from app.document_parser import DocumentParser, create_bid_from_parsed_content
from datetime import datetime
import io
import json
import time
import os
import uuid

bid_bp = Blueprint('bids', __name__)

@bid_bp.route('', methods=['GET'])
@jwt_required()
def get_bids():
    """获取标书列表"""
    user_id = get_jwt_identity()
    
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    status = request.args.get('status')
    company_id = request.args.get('company_id', type=int)
    search = request.args.get('search', '').strip()
    
    # 构建查询 - 通过公司关联检查权限
    query = db.session.query(Bid).join(Company).filter(Company.user_id == user_id)

    if status:
        query = query.filter(Bid.status == status)

    if company_id:
        # 验证用户是否拥有该公司
        company = Company.query.filter_by(id=company_id, user_id=user_id).first()
        if company:
            query = query.filter(Bid.company_id == company_id)
        else:
            return jsonify({'error': '无权限访问该公司的标书'}), 403
    
    if search:
        query = query.filter(
            or_(
                Bid.title.contains(search),
                Bid.description.contains(search)
            )
        )
    
    # 分页查询
    pagination = query.order_by(Bid.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    bids = []
    for bid in pagination.items:
        bid_dict = bid.to_dict()
        # 添加公司信息
        if bid.company:
            bid_dict['company'] = bid.company.to_dict()
        bids.append(bid_dict)
    
    return jsonify({
        'bids': bids,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': pagination.total,
            'pages': pagination.pages,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@bid_bp.route('/<int:bid_id>', methods=['GET'])
@jwt_required()
def get_bid(bid_id):
    """获取标书详情"""
    user_id = get_jwt_identity()
    
    # 通过公司关联检查权限
    bid = db.session.query(Bid).join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404
    
    bid_dict = bid.to_dict()
    
    # 添加公司信息
    if bid.company:
        bid_dict['company'] = bid.company.to_dict()
    
    # 添加关联的招标文件（新架构中通过tender_id直接关联）
    if bid.tender_document:
        bid_dict['tender_document'] = bid.tender_document.to_dict()
    else:
        bid_dict['tender_document'] = None
    
    # 添加引用的标书（新架构中通过BidReference表管理标书间引用关系）
    referenced_bids = []
    for bid_ref in bid.references:  # 这个标书引用的其他标书
        referenced_bids.append({
            'bid': bid_ref.target_bid.to_dict(),
            'reference_type': bid_ref.reference_type,
            'description': bid_ref.description
        })
    bid_dict['referenced_bids'] = referenced_bids
    
    return jsonify(bid_dict)

@bid_bp.route('', methods=['POST'])
@jwt_required()
def create_bid():
    """创建标书"""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    if not data:
        return jsonify({'error': '请求数据不能为空'}), 400
    
    title = data.get('title', '').strip()
    if not title:
        return jsonify({'error': '标书标题不能为空'}), 400
    
    company_id = data.get('company_id')
    if company_id:
        # 验证用户是否拥有该公司
        company = Company.query.filter_by(id=company_id, user_id=user_id).first()
        if not company:
            return jsonify({'error': '无权限使用该公司'}), 403
    
    # 创建标书
    bid = Bid(
        title=title,
        description=data.get('description'),
        content=data.get('content'),
        status=data.get('status', 'draft'),
        project_budget=data.get('project_budget'),
        project_location=data.get('project_location'),
        contact_person=data.get('contact_person'),
        contact_phone=data.get('contact_phone'),
        company_id=company_id
    )
    
    # 处理投标截止时间
    if data.get('tender_deadline'):
        try:
            bid.tender_deadline = datetime.fromisoformat(data['tender_deadline'].replace('Z', '+00:00'))
        except ValueError:
            return jsonify({'error': '投标截止时间格式错误'}), 400
    
    db.session.add(bid)
    db.session.flush()  # 获取bid.id
    
    # 关联招标文件（新架构中通过tender_id直接关联，只能关联一个）
    tender_document_ids = data.get('tender_document_ids', [])
    if tender_document_ids:
        # 只取第一个招标文件ID，因为新架构中一个标书只能关联一个招标文件
        tender_doc_id = tender_document_ids[0]
        tender_doc = TenderDocument.query.filter_by(id=tender_doc_id).first()
        if tender_doc:
            bid.tender_id = tender_doc_id
    
    # 注释掉：参考文件功能已删除
    # reference_document_ids = data.get('reference_document_ids', [])
    # for doc_id in reference_document_ids:
    #     ref_doc = ReferenceDocument.query.filter_by(id=doc_id, user_id=user_id).first()
    #     if ref_doc:
    #         bid_ref = BidReferenceDocument(bid_id=bid.id, reference_document_id=doc_id)
    #         db.session.add(bid_ref)
    
    try:
        db.session.commit()
        return jsonify({
            'msg': '标书创建成功',
            'bid': bid.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '创建失败'}), 500

@bid_bp.route('/<int:bid_id>', methods=['PUT'])
@jwt_required()
def update_bid(bid_id):
    """更新标书"""
    user_id = get_jwt_identity()
    
    # 通过公司关联检查权限
    bid = db.session.query(Bid).join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404
    
    data = request.get_json()
    if not data:
        return jsonify({'error': '请求数据不能为空'}), 400
    
    # 可更新的字段
    updatable_fields = [
        'title', 'description', 'content', 'status',
        'project_budget', 'project_location', 'contact_person', 'contact_phone'
    ]
    
    for field in updatable_fields:
        if field in data:
            setattr(bid, field, data[field])
    
    # 处理投标截止时间
    if 'tender_deadline' in data:
        if data['tender_deadline']:
            try:
                bid.tender_deadline = datetime.fromisoformat(data['tender_deadline'].replace('Z', '+00:00'))
            except ValueError:
                return jsonify({'error': '投标截止时间格式错误'}), 400
        else:
            bid.tender_deadline = None
    
    try:
        db.session.commit()
        return jsonify({
            'msg': '标书更新成功',
            'bid': bid.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '更新失败'}), 500

@bid_bp.route('/<int:bid_id>', methods=['DELETE'])
@jwt_required()
def delete_bid(bid_id):
    """删除标书"""
    user_id = get_jwt_identity()
    
    # 通过公司权限检查标书访问权限
    bid = db.session.query(Bid).join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404
    
    db.session.delete(bid)
    
    try:
        db.session.commit()
        return jsonify({'msg': '标书删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '删除失败'}), 500

@bid_bp.route('/generate', methods=['POST'])
@jwt_required()
def generate_bid_document():
    """生成标书文档"""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    if not data:
        return jsonify({'error': '请求数据不能为空'}), 400
    
    tender_document_ids = data.get('tender_document_ids', [])
    reference_bid_ids = data.get('reference_bid_ids', [])  # 改为引用其他标书
    company_id = data.get('company_id')

    if not tender_document_ids:
        return jsonify({'error': '请选择招标文件'}), 400

    # 验证公司权限，如果没有提供company_id，使用用户的第一个公司
    if company_id:
        company = Company.query.filter_by(id=company_id, user_id=user_id).first()
        if not company:
            return jsonify({'error': '无权限使用该公司'}), 403
    else:
        # 如果没有提供company_id，使用用户的第一个公司
        company = Company.query.filter_by(user_id=user_id).first()
        if not company:
            return jsonify({'error': '用户没有关联的公司，请先创建公司'}), 400
        company_id = company.id

    # 获取招标文件（现在是全局的）
    tender_docs = TenderDocument.query.filter(
        TenderDocument.id.in_(tender_document_ids)
    ).all()

    if len(tender_docs) != len(tender_document_ids):
        return jsonify({'error': '部分招标文件不存在'}), 404

    # 获取参考标书（用户自己的或同公司的）
    reference_bids = []
    if reference_bid_ids:
        reference_bids = db.session.query(Bid).join(Company).filter(
            Bid.id.in_(reference_bid_ids),
            Company.user_id == user_id
        ).all()
    
    # TODO: 实现AI生成标书逻辑
    # 这里应该调用AI服务来生成标书内容
    
    # 暂时返回模拟的生成结果
    generated_title = f"基于{tender_docs[0].title}的投标文件"
    generated_content = f"""
# {generated_title}

## 项目概述
根据招标文件要求，我们提交以下投标方案。

## 技术方案
[基于招标文件和参考标书生成的技术方案]

## 商务报价
[根据项目需求制定的报价方案]

## 公司资质
[公司相关资质和经验介绍]

---
生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
招标文件：{', '.join([doc.title for doc in tender_docs])}
参考标书：{', '.join([bid.title for bid in reference_bids])}
"""
    
    # 创建新的标书
    bid = Bid(
        title=generated_title,
        content=generated_content,
        status='draft',
        company_id=company_id,
        tender_id=tender_docs[0].id if tender_docs else None  # 关联主要招标文件
    )

    db.session.add(bid)
    db.session.flush()

    # 创建标书引用关系
    for ref_bid in reference_bids:
        bid_ref = BidReference(
            source_bid_id=bid.id,
            target_bid_id=ref_bid.id,
            reference_type='reference',
            description='生成时参考的标书'
        )
        db.session.add(bid_ref)
    
    try:
        db.session.commit()
        return jsonify({
            'msg': '标书生成成功',
            'bid': bid.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '生成失败'}), 500

@bid_bp.route('/<int:bid_id>/export', methods=['POST'])
@jwt_required()
def export_bid(bid_id):
    """导出标书"""
    user_id = get_jwt_identity()

    # 通过公司权限检查标书访问权限
    bid = db.session.query(Bid).join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404

    # 获取导出格式参数
    data = request.get_json() or {}
    export_format = data.get('format', 'docx').lower()

    if export_format not in ['docx', 'pdf', 'txt']:
        return jsonify({'error': '不支持的导出格式'}), 400

    # 生成安全的文件名（移除特殊字符）
    safe_title = "".join(c for c in bid.title if c.isalnum() or c in (' ', '-', '_')).strip()
    if not safe_title:
        safe_title = f"标书_{bid.id}"

    # 根据格式选择示例文件
    import os
    from flask import current_app

    static_dir = os.path.join(os.path.dirname(current_app.instance_path), 'static')
    if export_format == 'docx':
        sample_file = os.path.join(static_dir, 'sample_bid_export.docx')
        mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        file_extension = '.docx'
    elif export_format == 'pdf':
        sample_file = os.path.join(static_dir, 'sample_bid_export.pdf')
        mimetype = 'application/pdf'
        file_extension = '.pdf'
    else:  # txt
        sample_file = None  # txt格式直接生成内容
        mimetype = 'text/plain'
        file_extension = '.txt'

    # 检查示例文件是否存在，如果不存在则创建简单内容
    if sample_file and os.path.exists(sample_file):
        return send_file(
            sample_file,
            as_attachment=True,
            download_name=f"{safe_title}{file_extension}",
            mimetype=mimetype
        )
    else:
        # 如果示例文件不存在，返回文本内容
        content = f"""标书标题：{bid.title}

{bid.content or '暂无内容'}

导出时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
导出格式：{export_format.upper()}
"""

        # 创建内存文件
        output = io.BytesIO()
        output.write(content.encode('utf-8'))
        output.seek(0)

        return send_file(
            output,
            as_attachment=True,
            download_name=f"{safe_title}{file_extension}",
            mimetype=mimetype
        )

@bid_bp.route('/<int:bid_id>/export/progress', methods=['GET'])
@jwt_required()
def get_export_progress(bid_id):
    """获取导出进度（模拟）"""
    user_id = get_jwt_identity()

    # 通过公司关联检查权限
    bid = db.session.query(Bid).join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404

    # 模拟进度计算
    import random
    progress = min(100, random.randint(20, 100))

    return jsonify({
        'progress': progress,
        'status': 'processing' if progress < 100 else 'completed',
        'message': '正在生成文档...' if progress < 100 else '导出完成'
    })

@bid_bp.route('/import', methods=['POST'])
@jwt_required()
def import_bid():
    """导入标书"""
    user_id = get_jwt_identity()

    # 检查是否有文件上传
    if 'file' not in request.files:
        return jsonify({'error': '没有选择文件'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400

    # 获取表单数据
    title = request.form.get('title', '').strip()
    description = request.form.get('description', '').strip()
    company_id = request.form.get('company_id', type=int)

    if not title:
        return jsonify({'error': '标书标题不能为空'}), 400

    # 验证公司ID是否提供
    if not company_id:
        return jsonify({'error': '必须指定公司ID'}), 400

    # 验证公司权限
    company = Company.query.filter_by(id=company_id, user_id=user_id).first()
    if not company:
        return jsonify({'error': '无权限使用该公司或公司不存在'}), 403

    # 验证文件类型 - 使用更宽松的检测
    def is_valid_file_type(file):
        # 获取文件扩展名
        filename = file.filename.lower() if file.filename else ''
        extension = filename.split('.')[-1] if '.' in filename else ''

        # 允许的扩展名
        allowed_extensions = ['pdf', 'doc', 'docx']
        if extension not in allowed_extensions:
            return False

        # 允许的MIME类型（更宽松）
        allowed_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/doc',
            'application/ms-word',
            'application/word',
            'application/vnd.ms-word',
            'application/vnd.ms-word.document.macroEnabled.12',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
            'application/octet-stream',  # 通用类型
            ''  # 空MIME类型
        ]

        # 如果MIME类型匹配或扩展名正确，都允许
        return not file.content_type or file.content_type in allowed_types

    if not is_valid_file_type(file):
        return jsonify({'error': '不支持的文件类型，请上传PDF、DOC或DOCX文件'}), 400

    # 验证文件大小 (50MB)
    if len(file.read()) > 50 * 1024 * 1024:
        return jsonify({'error': '文件大小不能超过50MB'}), 400

    file.seek(0)  # 重置文件指针

    try:
        # 使用文件存储服务保存文件
        file_record, is_new = file_storage_service.save_file(file)

        # 获取文件路径用于解析
        file_path = file_storage_service.get_file_path(file_record)

        # 解析文档
        parser = DocumentParser()
        parsed_content = parser.parse_file(file_path)

        # 创建标书数据结构
        bid_structure = create_bid_from_parsed_content(
            parsed_content=parsed_content,
            title=title,
            description=description,
            user_id=None,  # 不再关联用户
            company_id=company_id
        )

        # 创建标书记录
        bid = Bid(
            title=bid_structure['bid']['title'],
            description=bid_structure['bid']['description'],
            content=bid_structure['bid']['content'],
            status=bid_structure['bid']['status'],
            root_node_id=bid_structure['bid']['root_node_id'],
            company_id=company_id,
            source_file_id=file_record.id  # 关联到上传的文件
        )

        db.session.add(bid)
        db.session.flush()  # 获取bid.id

        # 创建节点记录
        nodes_dict = {}  # 用于存储节点对象的字典
        for node_data in bid_structure['nodes']:
            node = BidNode(
                id=node_data['id'],
                node_type=node_data['node_type'],
                content=node_data['content'],
                node_metadata=node_data['node_metadata'],
                order_index=node_data['order_index'],
                bid_id=bid.id
            )
            db.session.add(node)
            nodes_dict[node_data['id']] = node  # 保存到字典中

        # 刷新以确保节点已经创建
        db.session.flush()

        # 创建中间表记录（为每个子节点创建与根节点的关联）
        root_node_id = bid_structure['bid']['root_node_id']

        for node_data in bid_structure['nodes'][1:]:  # 跳过根节点
            child_intermediate = BidIntermediate(
                parent_node_id=root_node_id,
                child_node_id=node_data['id']
            )
            db.session.add(child_intermediate)
            db.session.flush()  # 刷新以获取intermediate的ID

            # 更新子节点的parent_intermediate_id
            child_node = nodes_dict.get(node_data['id'])
            if child_node:
                child_node.parent_intermediate_id = child_intermediate.id

        db.session.commit()

        # 清理临时文件（如果需要的话，文件存储服务会处理）
        # 注释掉：文件已经通过file_storage_service管理，不需要手动清理
        # if os.path.exists(file_path):
        #     os.remove(file_path)

        return jsonify({
            'msg': '标书导入成功',
            'bid': bid.to_dict(),
            'metadata': bid_structure['metadata']
        }), 201

    except Exception as e:
        db.session.rollback()

        # 清理临时文件（如果需要的话，文件存储服务会处理）
        # 注释掉：文件已经通过file_storage_service管理，不需要手动清理
        # if 'file_path' in locals() and os.path.exists(file_path):
        #     os.remove(file_path)

        current_app.logger.error(f"标书导入失败: {str(e)}")
        return jsonify({'error': f'导入失败: {str(e)}'}), 500
